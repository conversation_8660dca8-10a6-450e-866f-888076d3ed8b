{"ai": {"provider": "qwen", "model": "Qwen/Qwen3-Coder-480B-A35B-Instruct", "apiKey": "${AI_REVIEW_API_KEY}", "baseURL": "${AI_REVIEW_BASE_URL}", "timeout": 30000}, "rules": {"ruleFiles": [".cursor/rules/project-rules.mdc"], "customPrompt": ""}, "severity": {"blockOnCritical": true, "warnOnMedium": true, "showLow": true}, "filters": {"includePatterns": ["**/*.java"], "excludePatterns": ["**/target/**", "**/test/**", "**/*Test.java", "**/generated/**"], "maxFileSizeKB": 500, "maxDiffLines": 1000}, "output": {"format": "terminal", "saveReport": false, "reportPath": ".ai-review-reports", "verbose": false}, "cache": {"enabled": true, "ttlHours": 24, "cachePath": ".git/.ai-review-cache"}, "failover": {"allowCommitOnTimeout": true, "allowCommitOnError": true, "maxRetries": 1}}