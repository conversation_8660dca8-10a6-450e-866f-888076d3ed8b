## Context
当前SyncCustHboneRelationJob定时任务在处理客户标签时，采用"物理删除+批量插入"的策略。这种方式存在以下问题：

1. **数据库锁冲突**：物理删除操作会对大量记录加锁，影响并发性能
2. **锁等待超时**：高并发场景下，DELETE操作可能导致其他线程等待超时
3. **数据不一致风险**：删除和插入操作之间存在时间窗口，可能导致数据短暂不一致
4. **性能瓶颈**：频繁的全量删除插入操作对数据库造成较大压力

**重要前提**：系统设计已经保证了同一个`wechatconscode`不会同时被多个线程处理，因此无需引入分布式锁机制。

## Goals / Non-Goals
**Goals**:
- 通过增量更新机制减少数据库操作压力
- 提升定时任务执行效率
- 保证数据一致性和完整性
- 增强系统的容错能力
- 减少数据库锁等待时间

**Non-Goals**:
- 不改变整体业务逻辑和功能
- 不影响其他模块的正常运行
- 不引入分布式锁（因为业务逻辑保证不会并发处理同一wechatconscode）
- 不引入新的外部依赖

## Decisions

### Decision 1: 采用增量更新策略替代删除插入模式
**选择**: 使用`incrementalUpdateCustTags`方法替代`physicalDeleteUserTags` + `batchInsertCustTags`
**原因**:
- 增量更新只处理变化的标签，大幅减少数据库操作
- 支持逻辑删除和恢复机制，保持数据历史
- 避免全量删除带来的锁竞争和性能问题
- 提高数据一致性的保证

### Decision 2: 优化事务边界
**选择**: 缩小事务范围，减少锁持有时间
**原因**:
- 将大事务拆分为小事务，降低锁等待概率
- 每个客户的标签处理在独立事务中完成
- 提高系统并发处理能力
- 减少死锁风险

### Decision 3: 优化批量处理逻辑
**选择**: 改进现有的批量处理策略
**原因**:
- 保持按企业员工分组的处理逻辑
- 优化批次大小，平衡性能和资源使用
- 增强错误隔离，单个批次失败不影响其他批次
- 添加更详细的监控和日志

### Decision 4: 启用增量更新方法的事务注解
**选择**: 取消`CmWechatCustTagRepository`中增量更新方法的事务注解注释
**原因**:
- 确保增量更新操作的事务完整性
- 利用Spring的事务管理机制
- 简化代码逻辑，提高可维护性

## Risks / Trade-offs

| Risk | Mitigation |
|------|------------|
| 增量更新逻辑复杂度增加 | 充分的单元测试和集成测试，详细的代码注释 |
| 事务边界调整可能影响数据一致性 | 仔细设计事务范围，确保关键操作的原子性 |
| 增量更新在大数据量下的性能影响 | 通过性能测试验证，必要时调整批量大小 |
| 逻辑删除可能导致数据膨胀 | 定期清理历史数据，设置数据保留策略 |

## Migration Plan

### 阶段1: 实现增量更新机制
1. 启用CmWechatCustTagRepository中的incrementalUpdateCustTags方法事务注解
2. 优化批量增量更新支持
3. 实现标签数据比较和差异计算逻辑

### 阶段2: 优化核心业务逻辑
1. 修改WechatCustRelationService.dealSingle方法，移除物理删除操作
2. 集成增量更新机制
3. 优化processBatch方法的标签处理逻辑

### 阶段3: 优化事务管理
1. 调整事务边界和传播行为
2. 优化批量处理逻辑，实现更好的错误隔离
3. 添加异常处理和回滚机制

### 阶段4: 监控和调优
1. 添加性能监控指标
2. 增强日志记录，包含详细的操作统计
3. 性能测试和调优

## Implementation Details

### 核心改动点

#### 1. WechatCustRelationService.dealSingle方法优化
```java
// 原有逻辑：
// 1. 物理删除所有标签 (physicalDeleteUserTags)
// 2. 批量插入新标签 (batchInsertCustTags)

// 优化后逻辑：
// 1. 移除physicalDeleteUserTags调用
// 2. 对每个客户使用incrementalUpdateCustTags进行增量更新
// 3. 保持现有的批量处理和并发机制
```

#### 2. CmWechatCustTagRepository增强
- 启用incrementalUpdateCustTags方法的事务注解（第191行）
- 启用physicalDeleteUserTags方法的事务注解（第114行）
- 启用batchInsertCustTags方法的事务注解（第135行）
- 优化CustTagSyncResult结果统计和日志输出

#### 3. 事务边界优化
- 每个客户的标签更新在独立事务中完成
- 缩小事务范围，减少锁持有时间
- 保持数据一致性的同时提升并发性能

#### 4. 监控和日志增强
- 添加标签操作的详细统计（新增、删除、恢复数量）
- 记录增量更新的执行时间
- 监控数据库操作次数和性能指标

## Open Questions

1. 增量更新机制在大数据量下的具体性能表现
2. 逻辑删除数据的定期清理策略
3. 监控指标的具体阈值设置和告警策略