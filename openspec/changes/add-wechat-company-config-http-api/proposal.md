# 变更提案：新增企微企业配置HTTP接口并优化同步任务

## Why

### 业务需求
- 当前系统缺少通过HTTP接口新增企微企业主体信息配置的功能，运维人员需要直接操作数据库来添加新企业配置
- 同步定时任务在处理第三方企业时需要从参数中手动指定企业编码，无法自动获取所有有效的第三方企业编码，增加了运维复杂度
- 需要支持新增配置后的缓存自动刷新机制，确保配置立即生效

### 技术驱动
- 现有缓存刷新机制依赖手动触发，新增配置后需要人工干预才能生效
- 同步任务参数格式固化，不支持灵活的企业筛选逻辑
- 缺乏统一的配置管理接口，不利于系统的可维护性

## 变更概述

### 变更目标
1. 新增HTTP接口用于新增"企微企业主体信息"配置，支持新增完成后自动刷新缓存
2. 优化同步客户群定时任务和同步客户群信息定时任务，新增corpType字段支持第三方处理逻辑

### 问题背景
- 当前系统缺少通过HTTP接口新增企微企业主体信息配置的功能
- 同步定时任务在处理第三方企业时需要从参数中获取企业编码，无法自动从配置表中获取所有有效的第三方企业编码
- 需要支持第三方企业配置新增后的缓存自动刷新机制

## 解决方案

### 方案1: 新增企业配置HTTP接口
- 新增Controller接口用于新增企微企业主体信息配置
- 集成缓存刷新逻辑，新增完成后自动调用缓存服务
- 支持参数验证和错误处理

### 方案2: 优化同步任务第三方处理逻辑
- 在SyncChatGroupJob和SyncChatGroupUserJob中新增corpType参数支持
- 当corpType标识为第三方时，从配置表中获取所有有效的企业编码
- 保持向后兼容性，支持原有的参数传递方式

## 技术实现

### 新增组件
1. **WechatCompanyConfigController**: 新增企微企业配置HTTP控制器
2. **新增请求响应对象**: 企业配置相关的请求和响应DTO
3. **缓存刷新集成**: 新增配置后自动刷新缓存

### 修改组件
1. **SyncChatGroupJob**: 新增corpType参数解析逻辑
2. **SyncChatGroupUserService**: 新增第三方企业批量获取逻辑

## 影响范围

### 影响的模块
- crm-wechat-service: HTTP接口和定时任务逻辑
- crm-wechat-client: 新增请求响应对象

### 数据库变更
- 无需数据库结构变更，使用现有的cm_wechat_company表

### API变更
- 新增HTTP POST接口: /wechatcompany/add
- 定时任务参数向后兼容，新增可选的corpType字段

## 风险评估

### 技术风险
- 缓存刷新失败可能影响配置生效
- 第三方企业批量同步可能增加系统负载

### 缓解措施
- 添加缓存刷新重试机制
- 限制批量同步的企业数量
- 完善错误处理和日志记录

## 验收标准

### 功能验收
1. 新增企业配置HTTP接口正常工作
2. 新增配置后缓存自动刷新生效
3. 同步任务支持corpType参数
4. 第三方企业同步逻辑正常工作

### 性能验收
1. 新增接口响应时间 < 1秒
2. 缓存刷新时间 < 500ms
3. 同步任务性能无明显下降

## 实施计划

### 第一阶段: 新增HTTP接口
1. 创建请求响应对象
2. 实现Controller接口
3. 集成缓存刷新逻辑
4. 单元测试和接口测试

### 第二阶段: 优化同步任务
1. 修改SyncChatGroupJob支持corpType参数
2. 修改SyncChatGroupUserService支持第三方批量处理
3. 集成测试
4. 性能测试

### 第三阶段: 部署和验证
1. 部署到测试环境
2. 功能验证测试
3. 生产环境部署
4. 监控和观察