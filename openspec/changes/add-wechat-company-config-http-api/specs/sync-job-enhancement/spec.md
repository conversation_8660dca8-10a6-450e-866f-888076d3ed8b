# 同步任务增强

## MODIFIED Requirements

### Requirement: 支持corpType参数的同步客户群任务

SyncChatGroupJob SHALL 支持corpType参数，当corpType为第三方应用时，自动从配置表获取所有有效的第三方企业编码进行同步。

#### Scenario: 支持corpType参数的同步客户群任务
- **Given**: SyncChatGroupJob接收到包含corpType参数的消息
- **When**: corpType参数值为"2"（第三方应用）时
- **Then**: 系统从CmWechatCorpConfigRepository.selectCorpConfigList()获取所有corpType="2"的有效企业编码
- **And**: 系统使用获取到的第三方企业编码列表执行同步操作
- **And**: 记录包含corpType信息的详细日志

### Requirement: 向后兼容的同步客户群任务

SyncChatGroupJob SHALL 保持向后兼容性，当不包含corpType参数时，按照原有逻辑处理。

#### Scenario: 向后兼容的同步客户群任务
- **Given**: SyncChatGroupJob接收到不包含corpType参数的消息（原有格式）
- **When**: 系统解析参数未找到corpType字段
- **Then**: 系统按照原有逻辑处理，使用MessageJobUtil.parseSyncCompanyNoList()解析企业编码
- **And**: 确保向后兼容性，不影响现有定时任务调度

### Requirement: 同步客户群任务参数解析增强

SyncChatGroupJob SHALL 支持同时包含companyNo和corpType的参数格式，并优先使用corpType逻辑。

#### Scenario: 同步客户群任务参数解析增强
- **Given**: SyncChatGroupJob接收到JSON格式的消息参数
- **When**: 系统解析JSON参数时
- **Then**: 系统支持同时包含companyNo和corpType参数
- **And**: 当corpType存在且为第三方时，优先使用corpType逻辑获取企业列表
- **And**: 当corpType不存在时，使用原有companyNo逻辑

### Requirement: 支持corpType参数的同步客户群信息任务

SyncChatGroupUserService SHALL 支持corpType参数，当corpType为第三方应用时，批量获取所有第三方企业的客户群进行同步。

#### Scenario: 支持corpType参数的同步客户群信息任务
- **Given**: SyncChatGroupUserService接收到包含corpType参数的arg参数
- **When**: corpType参数值为"2"（第三方应用）时
- **Then**: 系统从CmWechatCorpConfigRepository.selectCorpConfigList()获取所有corpType="2"的有效企业
- **And**: 系统获取这些企业的所有客户群（cm_wechat_group表）
- **And**: 系统对获取到的客户群列表执行批量同步操作
- **And**: 记录第三方企业批量同步的详细日志

### Requirement: 第三方企业客户群批量获取优化

SyncChatGroupUserService SHALL 优化第三方企业客户群批量获取性能，避免多次数据库查询。

#### Scenario: 第三方企业客户群批量获取优化
- **Given**: 系统需要获取所有第三方企业的客户群进行同步
- **When**: 系统调用cmWechatGroupRepository.listByCompanyNo()方法时
- **Then**: 系统传入从配置表中获取的所有第三方企业编码列表
- **And**: 系统优化查询性能，避免多次数据库查询
- **And**: 记录查询到的第三方企业数量和客户群数量

### Requirement: 同步任务错误处理增强

同步任务 SHALL 在处理corpType参数异常时提供降级机制和告警通知。

#### Scenario: 同步任务错误处理增强
- **Given**: 同步任务在处理corpType参数时发生异常
- **When**: 系统检测到corpType解析或配置查询异常
- **Then**: 系统记录详细的错误日志，包含corpType和异常信息
- **And**: 系统降级为原有逻辑处理，确保任务不会完全失败
- **And**: 系统发送告警通知相关人员

### Requirement: 同步任务性能监控

同步任务 SHALL 提供第三方企业批量同步的性能监控和告警机制。

#### Scenario: 同步任务性能监控
- **Given**: 同步任务执行第三方企业批量同步
- **When**: 任务开始和结束时
- **Then**: 系统记录任务开始时间、企业数量、客户群数量
- **And**: 系统计算并记录任务执行时长
- **And**: 当执行时间超过阈值时，系统记录性能告警日志

### Requirement: 同步任务参数验证

同步任务 SHALL 对corpType参数进行有效性验证，对无效参数进行适当处理。

#### Scenario: 同步任务参数验证
- **Given**: SyncChatGroupJob或SyncChatGroupUserService接收到无效的corpType参数
- **When**: 系统检测到corpType值不在允许范围内（1,2,3,4）
- **Then**: 系统记录参数无效的警告日志
- **And**: 系统忽略corpType参数，使用原有逻辑处理
- **And**: 确保任务继续执行而不中断

## ADDED Requirements

### Requirement: 支持配置表驱动的企业筛选

同步任务 SHALL 支持基于配置表的动态企业筛选功能，根据多个条件筛选需要同步的企业。

#### Scenario: 支持配置表驱动的企业筛选
- **Given**: 系统需要根据配置表动态筛选需要同步的企业
- **When**: 同步任务处理corpType参数时
- **Then**: 系统支持从配置表中根据recStat="1"（有效状态）筛选企业
- **And**: 系统支持根据envCode筛选当前环境的企业
- **And**: 系统支持根据corpType精确筛选企业类型

### Requirement: 同步任务执行状态跟踪

同步任务 SHALL 提供详细的执行状态跟踪功能，生成完整的执行报告。

#### Scenario: 同步任务执行状态跟踪
- **Given**: 同步任务执行第三方企业批量同步
- **When**: 任务执行过程中
- **Then**: 系统跟踪每个企业的同步状态（开始、进行中、成功、失败）
- **And**: 系统汇总任务执行结果，统计成功和失败的企业数量
- **And**: 系统生成任务执行报告，包含详细的状态信息