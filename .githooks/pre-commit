#!/bin/bash

##
# Git Hook - Pre-commit
# 在提交前调用AI对代码进行审查
##

# 记录hook被触发（用于调试）
echo "[$(date '+%Y-%m-%d %H:%M:%S')] pre-commit hook triggered" >> /tmp/git-hook-debug.log

# 检查是否安装了Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 未安装Node.js，无法执行AI代码审查"
    echo "   请安装Node.js后重试，或使用 git commit --no-verify 跳过检查"
    exit 1
fi

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 加载.env文件中的环境变量（如果存在）
if [ -f "$PROJECT_ROOT/.env" ]; then
    # 导出.env中的环境变量
    set -a
    source "$PROJECT_ROOT/.env"
    set +a
fi

# 调用AI代码审查工具
node "$PROJECT_ROOT/scripts/ai-code-review.js"

# 获取退出码
EXIT_CODE=$?

# 返回退出码
exit $EXIT_CODE
