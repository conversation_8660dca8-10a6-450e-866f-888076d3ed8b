/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.service.syncchatgroupuser.SyncChatGroupUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 同步群成员解析参数控制器
 * <AUTHOR>
 * @date 2025-08-27 17:12:01
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/syncchatgroupuser")
public class SyncChatGroupUserController {

    @Autowired
    private SyncChatGroupUserService syncChatGroupUserService;

    /**
     * @api {GET} /syncchatgroupuser/sync syncChatGroupUserData()
     * @apiVersion 1.0.0
     * @apiGroup SyncChatGroupUserController
     * @apiName 同步群成员数据接口
     * @apiDescription 拉取企微客户群的详情，并写入到数据库中
     * @apiParam (请求参数) {String} arg 同步参数，JSON格式字符串，支持chatId和companyNo参数
     * @apiParamExample 请求参数示例
     * arg={"chatId":"chat001,chat002","companyNo":"HBZC"}
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} returnObject 返回内容-同步结果信息
     * @apiSuccessExample 响应结果示例:
     */
    @GetMapping("/sync")
    public Response<String> syncChatGroupUserData(@RequestParam(required = false) String arg) {
        log.info("syncChatGroupUserData request, arg:{}", arg);
        try {
            syncChatGroupUserService.syncChatGroupUserData(arg);
            
            log.info("syncChatGroupUserData completed successfully, arg:{}", arg);
            return Response.ok("同步任务已启动");
        } catch (Exception e) {
            log.error("syncChatGroupUserData error, arg:{}, error:{}", arg, Throwables.getStackTraceAsString(e));
            return new Response<>(ResponseCodeEnum.SYS_ERROR.getCode(), "System error", null);
        }
    }

    @Autowired
    WechatExternalContactOuterService wechatExternalContactOuterService;
}