#!/usr/bin/env node

/**
 * AI代码审查工具
 * 使用魔塔社区Qwen3-coder模型审查Git暂存区的Java代码变更
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// 配置加载
function loadConfig() {
  const configPath = path.join(process.cwd(), '.ai-review.config.json');
  if (!fs.existsSync(configPath)) {
    console.error('❌ 配置文件不存在: .ai-review.config.json');
    process.exit(1);
  }
  
  let config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
  
  // 环境变量替换
  const replaceEnvVars = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        obj[key] = obj[key].replace(/\$\{(\w+)\}/g, (match, varName) => {
          return process.env[varName] || match;
        });
      } else if (typeof obj[key] === 'object') {
        replaceEnvVars(obj[key]);
      }
    }
  };
  replaceEnvVars(config);
  
  return config;
}

// 获取暂存区的Java文件变更
function getStagedJavaFiles() {
  try {
    const output = execSync('git diff --cached --name-only --diff-filter=ACM', { encoding: 'utf-8' });
    const files = output.trim().split('\n').filter(f => f && f.endsWith('.java'));
    return files;
  } catch (error) {
    console.error('❌ 获取暂存区文件失败:', error.message);
    return [];
  }
}

// 获取文件的diff内容
function getFileDiff(file) {
  try {
    const diff = execSync(`git diff --cached -U5 -- "${file}"`, { encoding: 'utf-8' });
    return diff;
  } catch (error) {
    console.warn(`⚠️  获取文件diff失败: ${file}`);
    return '';
  }
}

// 加载规则文件
function loadRules(config) {
  const rules = [];
  for (const ruleFile of config.rules.ruleFiles) {
    const rulePath = path.join(process.cwd(), ruleFile);
    if (fs.existsSync(rulePath)) {
      rules.push(fs.readFileSync(rulePath, 'utf-8'));
    } else {
      console.warn(`⚠️  规则文件不存在: ${ruleFile}`);
    }
  }
  return rules.join('\n\n');
}

// 计算diff的hash用于缓存
function calculateDiffHash(diffs) {
  const hash = crypto.createHash('md5');
  hash.update(JSON.stringify(diffs));
  return hash.digest('hex');
}

// 缓存管理
function getCachedResult(hash, config) {
  if (!config.cache.enabled) return null;
  
  const cacheDir = path.join(process.cwd(), config.cache.cachePath);
  const cacheFile = path.join(cacheDir, `${hash}.json`);
  
  if (!fs.existsSync(cacheFile)) return null;
  
  try {
    const cached = JSON.parse(fs.readFileSync(cacheFile, 'utf-8'));
    const age = Date.now() - cached.timestamp;
    const ttl = config.cache.ttlHours * 60 * 60 * 1000;
    
    if (age < ttl) {
      return cached.result;
    }
  } catch (error) {
    // 缓存读取失败，忽略
  }
  
  return null;
}

function setCachedResult(hash, result, config) {
  if (!config.cache.enabled) return;
  
  const cacheDir = path.join(process.cwd(), config.cache.cachePath);
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
  }
  
  const cacheFile = path.join(cacheDir, `${hash}.json`);
  fs.writeFileSync(cacheFile, JSON.stringify({
    timestamp: Date.now(),
    result: result
  }));
}

// 构建AI审查提示词
function buildPrompt(rules, diffs) {
  const systemPrompt = `你是一个Java代码审查专家，请严格按照以下项目规范审查代码：

${rules}

审查重点：
1. 命名规范（类名、方法名、变量名、包名）
2. 接口定义规范（Dubbo接口、APIDOC注释）
3. 事务管理（@Transactional注解、传播行为）
4. 异常处理（统一异常处理、错误码使用）
5. 安全规范（敏感数据、SQL注入）
6. 日志规范（级别使用、占位符）
7. 微信API调用规范
8. 注释规范（类注释、方法注释）

请对以下代码变更进行审查，并按问题严重程度分级输出。

**严格要求：只返回JSON格式，不要任何自然语言描述！**

返回格式：
{
  "severity": "critical" | "medium" | "low" | "none",
  "issues": [
    {
      "file": "文件路径",
      "lineRange": "行号范围",
      "level": "critical" | "medium" | "low",
      "title": "问题标题",
      "detail": "问题详细描述",
      "suggestion": "修复建议",
      "ruleRef": "违反的规范章节"
    }
  ],
  "summary": "审查总结"
}

severity定义：
- critical: 严重违反规范、安全漏洞、明显逻辑错误（阻止提交）
- medium: 注释不完整、日志使用不当、异常处理不完善（警告）
- low: 代码风格建议、命名优化建议（提示）
- none: 无问题

只审查变更的代码，不要审查已存在的未修改代码。`;

  const userPrompt = `请审查以下代码变更：

${diffs}`;

  return { systemPrompt, userPrompt };
}

// 调用AI API
async function callAI(systemPrompt, userPrompt, config) {
  const https = require('https');
  const http = require('http');
  
  const url = new URL(config.ai.baseURL);
  const isHttps = url.protocol === 'https:';
  const client = isHttps ? https : http;
  
  const requestBody = JSON.stringify({
    model: config.ai.model,
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ],
    temperature: 0,
    top_p: 1,
    max_tokens: 4096
  });
  
  return new Promise((resolve, reject) => {
    // 构建正确的API路径
    let apiPath = url.pathname || '';
    // 移除末尾的斜杠
    apiPath = apiPath.replace(/\/$/, '');
    // 如果路径不以/v1结尾，添加/v1
    if (!apiPath.endsWith('/v1')) {
      apiPath += '/v1';
    }
    apiPath += '/chat/completions';
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: apiPath,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.ai.apiKey}`,
        'Content-Length': Buffer.byteLength(requestBody)
      },
      timeout: config.ai.timeout
    };
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            const content = response.choices[0].message.content;
            resolve(content);
          } catch (error) {
            reject(new Error(`解析响应失败: ${error.message}`));
          }
        } else {
          reject(new Error(`API调用失败: ${res.statusCode} - ${data}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    req.write(requestBody);
    req.end();
  });
}

// 解析AI响应
function parseAIResponse(content) {
  try {
    // 尝试提取JSON（可能包含在markdown代码块中）
    let jsonStr = content.trim();
    const jsonMatch = jsonStr.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      jsonStr = jsonMatch[1];
    }
    
    const result = JSON.parse(jsonStr);
    
    // 验证格式
    if (!result.severity || !Array.isArray(result.issues)) {
      throw new Error('响应格式不正确');
    }
    
    return result;
  } catch (error) {
    console.error('❌ 解析AI响应失败:', error.message);
    console.error('原始响应:', content);
    return null;
  }
}

// 格式化输出审查结果
function formatOutput(result, config) {
  const icons = {
    critical: '🚨',
    medium: '⚠️',
    low: '💡',
    none: '✅'
  };
  
  const icon = icons[result.severity] || '❓';
  
  console.log('\n' + '='.repeat(80));
  console.log(`${icon} AI代码审查结果 - ${result.severity.toUpperCase()}`);
  console.log('='.repeat(80));
  
  if (result.issues.length === 0) {
    console.log('\n✅ 代码审查通过，未发现问题！\n');
    return;
  }
  
  // 按级别分组
  const critical = result.issues.filter(i => i.level === 'critical');
  const medium = result.issues.filter(i => i.level === 'medium');
  const low = result.issues.filter(i => i.level === 'low');
  
  if (critical.length > 0) {
    console.log(`\n🚨 高级问题 (${critical.length}个) - 必须修复:\n`);
    critical.forEach((issue, index) => {
      console.log(`${index + 1}. 【${issue.title}】`);
      console.log(`   文件: ${issue.file}:${issue.lineRange}`);
      console.log(`   问题: ${issue.detail}`);
      console.log(`   建议: ${issue.suggestion}`);
      if (issue.ruleRef) console.log(`   规范: ${issue.ruleRef}`);
      console.log('');
    });
  }
  
  if (medium.length > 0 && config.severity.warnOnMedium) {
    console.log(`\n⚠️  中级问题 (${medium.length}个) - 建议修复:\n`);
    medium.forEach((issue, index) => {
      console.log(`${index + 1}. 【${issue.title}】`);
      console.log(`   文件: ${issue.file}:${issue.lineRange}`);
      console.log(`   问题: ${issue.detail}`);
      console.log(`   建议: ${issue.suggestion}`);
      console.log('');
    });
  }
  
  if (low.length > 0 && config.severity.showLow) {
    console.log(`\n💡 低级建议 (${low.length}个):\n`);
    low.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.title} - ${issue.file}:${issue.lineRange}`);
    });
    console.log('');
  }
  
  if (result.summary) {
    console.log(`\n📝 总结: ${result.summary}\n`);
  }
  
  console.log('='.repeat(80) + '\n');
}

// 主函数
async function main() {
  console.log('🤖 AI代码审查启动...\n');
  
  const config = loadConfig();
  
  // 检查API配置
  if (!config.ai.apiKey || config.ai.apiKey.startsWith('${')) {
    console.error('❌ 未配置AI_REVIEW_API_KEY环境变量');
    if (config.failover.allowCommitOnError) {
      console.warn('⚠️  配置错误，但允许提交（failover模式）\n');
      process.exit(0);
    }
    process.exit(1);
  }
  
  if (!config.ai.baseURL || config.ai.baseURL.startsWith('${')) {
    console.error('❌ 未配置AI_REVIEW_BASE_URL环境变量');
    if (config.failover.allowCommitOnError) {
      console.warn('⚠️  配置错误，但允许提交（failover模式）\n');
      process.exit(0);
    }
    process.exit(1);
  }
  
  // 获取暂存区Java文件
  const files = getStagedJavaFiles();
  
  if (files.length === 0) {
    console.log('ℹ️  没有Java文件变更，跳过审查\n');
    process.exit(0);
  }
  
  console.log(`📁 发现 ${files.length} 个Java文件变更:`);
  files.forEach(f => console.log(`   - ${f}`));
  console.log('');
  
  // 获取diff
  const diffs = files.map(file => {
    const diff = getFileDiff(file);
    return `### 文件: ${file}\n${diff}\n`;
  }).join('\n');
  
  if (!diffs.trim()) {
    console.log('ℹ️  没有实际代码变更，跳过审查\n');
    process.exit(0);
  }
  
  // 检查缓存
  const diffHash = calculateDiffHash(diffs);
  const cachedResult = getCachedResult(diffHash, config);
  
  let result;
  if (cachedResult) {
    console.log('💾 使用缓存的审查结果\n');
    result = cachedResult;
  } else {
    console.log('🔍 正在调用AI进行代码审查...\n');
    
    try {
      // 加载规则
      const rules = loadRules(config);
      
      // 构建提示词
      const { systemPrompt, userPrompt } = buildPrompt(rules, diffs);
      
      // 调用AI
      const content = await callAI(systemPrompt, userPrompt, config);
      
      // 解析响应
      result = parseAIResponse(content);
      
      if (!result) {
        throw new Error('无法解析AI响应');
      }
      
      // 缓存结果
      setCachedResult(diffHash, result, config);
      
    } catch (error) {
      console.error(`❌ AI调用失败: ${error.message}`);
      if (config.failover.allowCommitOnError) {
        console.warn('⚠️  AI服务不可用，但允许提交（failover模式）\n');
        process.exit(0);
      }
      process.exit(1);
    }
  }
  
  // 输出结果
  formatOutput(result, config);
  
  // 根据严重程度决定是否阻止提交
  if (result.severity === 'critical' && config.severity.blockOnCritical) {
    console.error('❌ 发现高级问题，提交已阻止！');
    console.error('   请修复上述问题后重新提交');
    console.error('   或使用 git commit --no-verify 跳过检查\n');
    process.exit(1);
  }
  
  if (result.severity === 'medium') {
    console.warn('⚠️  发现中级问题，建议修复后再提交\n');
  }
  
  console.log('✅ 代码审查通过，允许提交\n');
  process.exit(0);
}

// 运行
main().catch(error => {
  console.error('❌ 程序异常:', error);
  const config = loadConfig();
  if (config.failover.allowCommitOnError) {
    console.warn('⚠️  程序异常，但允许提交（failover模式）\n');
    process.exit(0);
  }
  process.exit(1);
});
