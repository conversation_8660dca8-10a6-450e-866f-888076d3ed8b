#!/bin/bash

##
# AI代码审查系统演示脚本
# 模拟实际使用场景
##

echo "🎬 AI代码审查系统演示"
echo "======================================"
echo ""

# 场景1: 无API配置时的降级处理
echo "📋 场景1: 未配置API时的降级处理"
echo "--------------------------------------"
echo "模拟: AI_REVIEW_API_KEY 和 AI_REVIEW_BASE_URL 未配置"
echo ""
echo "运行: node scripts/ai-code-review.js"
echo ""
echo "预期结果:"
echo "  ❌ 未配置AI_REVIEW_API_KEY环境变量"
echo "  ⚠️  配置错误，但允许提交（failover模式）"
echo ""
read -p "按回车继续..."
echo ""

# 场景2: 无Java文件变更
echo "📋 场景2: 没有Java文件变更"
echo "--------------------------------------"
echo "当暂存区没有.java文件时"
echo ""
echo "预期结果:"
echo "  ℹ️  没有Java文件变更，跳过审查"
echo ""
read -p "按回车继续..."
echo ""

# 场景3: 正常审查流程
echo "📋 场景3: 正常审查流程(需要配置API)"
echo "--------------------------------------"
echo "配置环境变量:"
echo "  export AI_REVIEW_BASE_URL='https://dashscope.aliyuncs.com/compatible-mode'"
echo "  export AI_REVIEW_API_KEY='sk-your-api-key'"
echo ""
echo "提交有Java文件变更的代码:"
echo "  git commit -m 'feat: 添加新功能'"
echo ""
echo "审查过程:"
echo "  1. 🤖 AI代码审查启动..."
echo "  2. 📁 发现 N 个Java文件变更"
echo "  3. 🔍 正在调用AI进行代码审查..."
echo "  4. ⏱️  等待5-15秒"
echo "  5. 📊 显示审查结果"
echo ""
echo "可能的结果:"
echo "  A) ✅ 代码审查通过 → 正常提交"
echo "  B) 🚨 发现高级问题 → 阻止提交"
echo "  C) ⚠️  发现中级问题 → 警告但允许提交"
echo ""
read -p "按回车继续..."
echo ""

# 场景4: 跳过审查
echo "📋 场景4: 紧急情况跳过审查"
echo "--------------------------------------"
echo "使用 --no-verify 参数:"
echo "  git commit -m 'hotfix: 紧急修复' --no-verify"
echo ""
echo "预期结果:"
echo "  跳过pre-commit hook，直接提交"
echo ""
read -p "按回车继续..."
echo ""

# 场景5: 缓存机制
echo "📋 场景5: 缓存机制"
echo "--------------------------------------"
echo "第一次提交相同代码:"
echo "  - 调用AI API"
echo "  - 耗时10-15秒"
echo "  - 结果缓存到 .git/.ai-review-cache/"
echo ""
echo "24小时内再次提交相同代码:"
echo "  - 💾 使用缓存的审查结果"
echo "  - 耗时 < 1秒"
echo ""
read -p "按回车继续..."
echo ""

# 实际测试
echo "🧪 实际测试"
echo "======================================"
echo ""
echo "让我们实际运行一次AI审查工具(无API配置):"
echo ""
read -p "按回车开始测试..."
echo ""

node scripts/ai-code-review.js

echo ""
echo "======================================"
echo "✅ 演示完成!"
echo ""
echo "📚 详细使用说明请查看:"
echo "   - AI-REVIEW-GUIDE.md (完整使用指南)"
echo "   - .ai-review.config.json (配置文件)"
echo "   - .cursor/rules/project-rules.mdc (审查规则)"
echo ""
echo "🚀 快速开始:"
echo "   1. 配置 .env 文件中的 AI_REVIEW_BASE_URL 和 AI_REVIEW_API_KEY"
echo "   2. 修改一个Java文件"
echo "   3. git commit -m 'test: 测试AI审查'"
echo "   4. 观察审查过程"
echo ""
