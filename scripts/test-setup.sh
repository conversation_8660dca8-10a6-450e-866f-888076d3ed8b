#!/bin/bash

##
# AI代码审查系统测试脚本
##

echo "🧪 开始测试AI代码审查系统..."
echo ""

# 1. 检查文件是否存在
echo "1️⃣ 检查文件..."
files=(
    ".githooks/pre-commit"
    "scripts/ai-code-review.js"
    "scripts/setup-hooks.sh"
    ".ai-review.config.json"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file 不存在"
        exit 1
    fi
done
echo ""

# 2. 检查Git配置
echo "2️⃣ 检查Git配置..."
HOOKS_PATH=$(git config core.hooksPath)
if [ "$HOOKS_PATH" = ".githooks" ]; then
    echo "   ✅ Git hooks路径配置正确: $HOOKS_PATH"
else
    echo "   ❌ Git hooks路径配置错误: $HOOKS_PATH"
    exit 1
fi
echo ""

# 3. 检查执行权限
echo "3️⃣ 检查执行权限..."
if [ -x ".githooks/pre-commit" ]; then
    echo "   ✅ pre-commit 有执行权限"
else
    echo "   ❌ pre-commit 无执行权限"
    exit 1
fi

if [ -x "scripts/setup-hooks.sh" ]; then
    echo "   ✅ setup-hooks.sh 有执行权限"
else
    echo "   ❌ setup-hooks.sh 无执行权限"
    exit 1
fi
echo ""

# 4. 检查Node.js环境
echo "4️⃣ 检查Node.js环境..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    echo "   ✅ Node.js已安装: $NODE_VERSION"
else
    echo "   ❌ Node.js未安装"
    exit 1
fi
echo ""

# 5. 检查配置文件语法
echo "5️⃣ 检查配置文件..."
if node -e "JSON.parse(require('fs').readFileSync('.ai-review.config.json', 'utf-8'))" 2>/dev/null; then
    echo "   ✅ .ai-review.config.json 格式正确"
else
    echo "   ❌ .ai-review.config.json 格式错误"
    exit 1
fi
echo ""

# 6. 检查环境变量
echo "6️⃣ 检查环境变量..."
if [ -z "$AI_REVIEW_API_KEY" ]; then
    echo "   ⚠️  AI_REVIEW_API_KEY 未设置"
    echo "      这是正常的，首次使用时需要配置"
else
    echo "   ✅ AI_REVIEW_API_KEY 已设置"
fi

if [ -z "$AI_REVIEW_BASE_URL" ]; then
    echo "   ⚠️  AI_REVIEW_BASE_URL 未设置"
    echo "      这是正常的，首次使用时需要配置"
else
    echo "   ✅ AI_REVIEW_BASE_URL 已设置"
fi
echo ""

# 7. 测试脚本语法
echo "7️⃣ 测试脚本语法..."
if node -c scripts/ai-code-review.js 2>/dev/null; then
    echo "   ✅ ai-code-review.js 语法正确"
else
    echo "   ❌ ai-code-review.js 语法错误"
    exit 1
fi
echo ""

# 总结
echo "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "="
echo "✅ 所有测试通过！AI代码审查系统已就绪"
echo "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "="
echo ""
echo "📝 下一步:"
echo "   1. 在 .env 文件中配置 AI_REVIEW_BASE_URL 和 AI_REVIEW_API_KEY"
echo "   2. 或设置环境变量:"
echo "      export AI_REVIEW_BASE_URL='https://dashscope.aliyuncs.com/compatible-mode'"
echo "      export AI_REVIEW_API_KEY='sk-xxxxx'"
echo "   3. 提交代码时会自动触发AI审查"
echo ""
echo "💡 测试提示:"
echo "   - 修改一个Java文件并提交，观察审查过程"
echo "   - 使用 git commit --no-verify 可以跳过审查"
echo ""
