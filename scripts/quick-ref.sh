#!/bin/bash

##
# AI代码审查 - 快速参考
##

cat << 'EOF'
╔═══════════════════════════════════════════════════════════════╗
║           AI代码审查系统 - 快速参考卡片                      ║
╚═══════════════════════════════════════════════════════════════╝

📦 首次安装 (每个开发者拉取代码后执行一次)
─────────────────────────────────────────────────────────────────
  bash scripts/setup-hooks.sh

⚙️  配置API (在 .env 文件中添加)
─────────────────────────────────────────────────────────────────
  AI_REVIEW_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode
  AI_REVIEW_API_KEY=sk-your-api-key-here

💻 日常使用
─────────────────────────────────────────────────────────────────
  git add .
  git commit -m "feat: 添加新功能"
  
  # 自动触发AI审查，耗时5-15秒
  # 发现高级问题会阻止提交，需要修复后重试

🚨 紧急提交 (跳过审查)
─────────────────────────────────────────────────────────────────
  git commit -m "hotfix: 紧急修复" --no-verify

🔍 手动测试审查工具
─────────────────────────────────────────────────────────────────
  node scripts/ai-code-review.js

📊 问题级别
─────────────────────────────────────────────────────────────────
  🚨 高级(Critical)  - 阻止提交，必须修复
     • 严重违反项目规范
     • 安全漏洞
     • 事务管理错误
     • Dubbo接口定义不规范
     
  ⚠️  中级(Medium)    - 警告提示，建议修复
     • 注释不完整
     • 日志使用不当
     • 异常处理不完善
     
  💡 低级(Low)       - 建议提示
     • 代码风格优化
     • 命名建议

🛠️  故障排查
─────────────────────────────────────────────────────────────────
  # Hook未生效
  git config core.hooksPath
  # 应该输出: .githooks
  
  # 重新安装
  bash scripts/setup-hooks.sh
  
  # 测试系统
  bash scripts/test-setup.sh
  
  # 演示系统
  bash scripts/demo.sh

📚 详细文档
─────────────────────────────────────────────────────────────────
  AI-REVIEW-GUIDE.md           - 完整使用指南
  .ai-review.config.json       - 配置文件
  .cursor/rules/project-rules.mdc  - 审查规则

💡 最佳实践
─────────────────────────────────────────────────────────────────
  ✓ 小步提交，每次提交少量文件
  ✓ 提交前自己先检查代码规范
  ✓ 发现问题及时修复，不要积累
  ✓ 只在紧急情况使用 --no-verify
  ✓ 遵循 .cursor/rules/project-rules.mdc 编码规范

🔧 配置调整
─────────────────────────────────────────────────────────────────
  文件: .ai-review.config.json
  
  # 更严格 (生产环境推荐)
  "severity": {
    "blockOnCritical": true
  }
  "failover": {
    "allowCommitOnTimeout": false,
    "allowCommitOnError": false
  }
  
  # 更宽松 (开发阶段)
  "severity": {
    "blockOnCritical": false
  }
  "failover": {
    "allowCommitOnTimeout": true,
    "allowCommitOnError": true
  }

🆘 获取帮助
─────────────────────────────────────────────────────────────────
  联系: 项目负责人
  文档: AI-REVIEW-GUIDE.md

╔═══════════════════════════════════════════════════════════════╗
║  版本: 1.0.0  |  更新: 2025-11-04  |  模型: Qwen3-coder     ║
╚═══════════════════════════════════════════════════════════════╝
EOF
