#!/bin/bash

##
# Git Hooks 安装脚本
# 配置Git使用仓库内的.githooks目录
##

echo "🔧 正在配置Git Hooks..."

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 配置Git使用.githooks目录
cd "$PROJECT_ROOT"
git config core.hooksPath .githooks

if [ $? -eq 0 ]; then
    echo "✅ Git Hooks 配置成功！"
    echo ""
    echo "已配置使用 .githooks 目录中的钩子脚本"
    echo ""
    
    # 设置执行权限
    chmod +x .githooks/pre-commit
    echo "✅ 已设置pre-commit钩子的执行权限"
    echo ""
    
    # 检查环境变量配置
    echo "📋 环境变量检查:"
    if [ -z "$AI_REVIEW_API_KEY" ]; then
        echo "   ⚠️  未设置 AI_REVIEW_API_KEY"
        echo "      请在 .env 文件或环境变量中配置"
    else
        echo "   ✅ AI_REVIEW_API_KEY 已配置"
    fi
    
    if [ -z "$AI_REVIEW_BASE_URL" ]; then
        echo "   ⚠️  未设置 AI_REVIEW_BASE_URL"
        echo "      请在 .env 文件或环境变量中配置"
    else
        echo "   ✅ AI_REVIEW_BASE_URL 已配置"
    fi
    
    echo ""
    echo "💡 提示:"
    echo "   - 代码提交时会自动触发AI审查"
    echo "   - 发现高级问题时会阻止提交"
    echo "   - 如需跳过检查，使用: git commit --no-verify"
    echo ""
else
    echo "❌ Git Hooks 配置失败"
    exit 1
fi
