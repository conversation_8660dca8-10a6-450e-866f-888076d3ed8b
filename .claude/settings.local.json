{"permissions": {"allow": ["Bash(rg:*)", "<PERSON><PERSON>(claude mcp)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:developer.work.weixin.qq.com)", "WebSearch", "<PERSON><PERSON>(mv:*)", "mcp__mysql-mcp__execute_sql", "mcp__tapd-mcp__get_stories_or_tasks", "mcp__tapd-mcp__get_iterations", "Bash(mvn clean compile:*)", "<PERSON><PERSON>(openspec:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)"], "defaultMode": "acceptEdits"}}