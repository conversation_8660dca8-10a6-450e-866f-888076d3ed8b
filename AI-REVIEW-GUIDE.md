# AI代码审查系统 - 使用指南

## 📦 已安装组件

1. **Git Hook**: `.githooks/pre-commit` - 在提交时自动触发审查
2. **审查工具**: `scripts/ai-code-review.js` - AI代码审查核心脚本
3. **配置文件**: `.ai-review.config.json` - 审查配置
4. **安装脚本**: `scripts/setup-hooks.sh` - 团队成员安装Hook用

## 🚀 团队成员首次使用

### 1. 拉取代码后执行安装
```bash
bash scripts/setup-hooks.sh
```

### 2. 配置环境变量

在 `.env` 文件中添加(或设置系统环境变量):
```bash
# 魔塔社区Qwen3-coder配置
AI_REVIEW_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode
AI_REVIEW_API_KEY=sk-xxxxxxxxxxxxx
```

**获取API Key的方式:**
- 访问魔塔社区/阿里云灵积平台
- 开通Qwen3-coder模型服务
- 生成API Key

### 3. 验证配置
```bash
# 设置测试环境变量
export AI_REVIEW_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode"
export AI_REVIEW_API_KEY="sk-xxxxx"

# 手动测试审查工具
node scripts/ai-code-review.js
```

## 💻 日常使用

### 正常提交
```bash
git add .
git commit -m "feat: 添加新功能"
# 自动触发AI审查，耗时5-15秒
```

### 审查结果示例

**✅ 无问题 - 正常提交**
```
🤖 AI代码审查启动...
📁 发现 1 个Java文件变更:
   - crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/WechatUserService.java
🔍 正在调用AI进行代码审查...

✅ 代码审查通过，未发现问题！
✅ 代码审查通过，允许提交
```

**🚨 发现高级问题 - 阻止提交**
```
🚨 高级问题 (2个) - 必须修复:

1. 【Repository类缺少@Transactional注解】
   文件: WechatUserRepository.java:45-52
   问题: Repository类的方法未使用@Transactional注解进行事务管理
   建议: 添加@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
   规范: 事务管理规范 - Repository层事务注解

2. 【Dubbo接口缺少APIDOC注释】
   文件: WechatAuthFacade.java:12-20
   问题: Dubbo接口未按规范添加完整的APIDOC风格注释
   建议: 参考项目规范添加@api、@apiVersion、@apiParam等注释
   规范: 接口定义规范 - Dubbo接口注释

❌ 发现高级问题，提交已阻止！
   请修复上述问题后重新提交
   或使用 git commit --no-verify 跳过检查
```

**⚠️ 发现中级问题 - 警告提示**
```
⚠️  中级问题 (1个) - 建议修复:

1. 【方法缺少完整注释】
   文件: WechatUserService.java:78-85
   问题: 方法注释缺少@description、@param、@return说明
   建议: 补充完整的方法注释

✅ 代码审查通过，允许提交
```

### 跳过审查(紧急情况)
```bash
git commit -m "hotfix: 紧急修复" --no-verify
```

## ⚙️ 配置说明

### `.ai-review.config.json` 主要配置项

```json
{
  "ai": {
    "provider": "qwen",           // AI提供商
    "model": "Qwen3-coder",       // 模型名称
    "timeout": 30000              // 超时时间(毫秒)
  },
  "severity": {
    "blockOnCritical": true,      // 发现高级问题时阻止提交
    "warnOnMedium": true,         // 显示中级问题警告
    "showLow": true               // 显示低级建议
  },
  "filters": {
    "includePatterns": ["**/*.java"],  // 只审查Java文件
    "excludePatterns": [
      "**/target/**",             // 排除编译输出
      "**/test/**",               // 排除测试代码
      "**/*Test.java"
    ],
    "maxFileSizeKB": 500          // 单文件大小限制
  },
  "cache": {
    "enabled": true,              // 启用缓存
    "ttlHours": 24                // 缓存有效期
  },
  "failover": {
    "allowCommitOnTimeout": true, // 超时时允许提交
    "allowCommitOnError": true    // 错误时允许提交
  }
}
```

### 调整严格程度

**更严格(推荐生产环境):**
```json
{
  "severity": {
    "blockOnCritical": true,
    "warnOnMedium": true
  },
  "failover": {
    "allowCommitOnTimeout": false,
    "allowCommitOnError": false
  }
}
```

**更宽松(开发阶段):**
```json
{
  "severity": {
    "blockOnCritical": false,  // 只警告不阻止
    "warnOnMedium": true
  },
  "failover": {
    "allowCommitOnTimeout": true,
    "allowCommitOnError": true
  }
}
```

## 🔍 问题分级标准

### 🚨 高级问题(Critical) - 阻止提交
- 严重违反项目规范(包名错误、命名不符合规范)
- 安全漏洞(SQL注入风险、敏感信息泄露)
- 明显的逻辑错误(空指针风险、资源泄漏)
- 事务管理错误(缺少注解、传播行为不当)
- 微信API调用未遵循规范
- Dubbo接口定义不符合规范

### ⚠️ 中级问题(Medium) - 警告提示
- 代码注释不完整
- 日志级别使用不当
- 异常处理不够完善
- 性能优化建议

### 💡 低级问题(Low) - 建议提示
- 代码风格建议
- 变量命名优化建议
- 代码简化建议

## 🛠️ 故障排查

### 问题1: Hook未生效
```bash
# 检查配置
git config core.hooksPath

# 应该输出: .githooks
# 如果没有，重新运行安装脚本
bash scripts/setup-hooks.sh
```

### 问题2: AI调用失败
```bash
# 检查环境变量
echo $AI_REVIEW_BASE_URL
echo $AI_REVIEW_API_KEY

# 测试网络连接
curl -I https://dashscope.aliyuncs.com

# 查看详细错误，手动运行
node scripts/ai-code-review.js
```

### 问题3: 审查速度慢
- 检查网络连接
- 启用缓存(默认已启用)
- 减少单次提交的文件数量
- 调整超时时间

### 问题4: 误判问题
- AI可能存在误判，可通过`--no-verify`跳过
- 将误判案例反馈给团队，优化规则
- 调整`.ai-review.config.json`中的严格程度

## 📊 性能优化

### 缓存机制
- 相同的代码变更会使用缓存结果
- 缓存位置: `.git/.ai-review-cache/`
- 缓存有效期: 24小时(可配置)

### 增量审查
- 只审查暂存区的变更内容
- 不会重复审查未修改的代码
- 大文件只传变更的diff

## 🔐 安全说明

### API Key保护
- **不要**将API Key提交到Git仓库
- 使用环境变量或`.env`文件配置
- `.env`文件已在`.gitignore`中排除

### 代码隐私
- 代码会发送到魔塔/阿里云服务器进行审查
- 如果有隐私要求，可部署本地Qwen3-coder服务
- 修改`AI_REVIEW_BASE_URL`指向本地服务

## 🤝 团队协作

### 统一配置管理
- `.githooks/`、`scripts/`、`.ai-review.config.json` 已纳入版本控制
- 配置更新会随代码pull自动同步
- 团队成员只需首次运行`setup-hooks.sh`

### CI/CD集成(待实现)
- 在CI流水线中也执行相同审查
- 作为强制防线，防止本地跳过
- 可生成审查报告归档

## 📝 最佳实践

1. **小步提交**: 每次提交少量文件，审查更快更准确
2. **遵循规范**: 按照`.cursor/rules/project-rules.mdc`编码
3. **及时修复**: 发现问题及时修复，不要积累
4. **合理跳过**: 只在紧急情况使用`--no-verify`
5. **反馈改进**: 发现误判或遗漏及时反馈

## 🆘 获取帮助

- 查看审查规则: `.cursor/rules/project-rules.mdc`
- 查看配置文件: `.ai-review.config.json`
- 手动测试: `node scripts/ai-code-review.js`
- 联系团队负责人

---

**版本**: 1.0.0  
**更新日期**: 2025-11-04
