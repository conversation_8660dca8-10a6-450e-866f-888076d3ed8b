# CRM微信企业应用管理系统

## 项目简介

基于Spring Boot + Spring Cloud + Dubbo 3.x的微信CRM系统，采用多模块Maven项目结构，支持多租户微信企业应用管理。系统集成了微信企业内建应用和第三方应用的完整功能，包括客户管理、群聊管理、消息推送、素材管理等核心业务功能。

## 技术架构

### 核心技术栈

- **JDK**: 1.8
- **Spring Boot**: 2.3.7.RELEASE
- **Spring Cloud**: Hoxton.SR9
- **Dubbo**: 3.2.12
- **MyBatis**: 2.2.2
- **数据库**: MySQL 8.x
- **缓存**: Redis
- **注册中心**: ZooKeeper 3.4.13
- **连接池**: Druid 1.2.8

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   HTTP接口      │    │   Dubbo服务     │
│   (Vue/微信)    │───▶│  Controller层   │───▶│   Facade层      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信API       │    │   外部服务      │    │   业务服务      │
│   (企业微信)    │◀───│  OuterService   │◀───│   Service层     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储      │    │   数据访问      │    │   缓存层        │
│   MySQL/Redis   │◀───│  Repository层   │◀───│   Redis         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境依赖

### 基础环境

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 5.0+
- **ZooKeeper**: 3.4.13+

### 开发工具

- **IDE**: IntelliJ IDEA / Eclipse
- **版本控制**: Git
- **API测试**: Postman / Swagger

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd crm-wechat
```

### 2. 环境准备

```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 启动Redis和ZooKeeper
redis-server
zkServer.sh start
```

### 3. 数据库初始化

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE crm_wechat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 执行SQL脚本（项目根目录下的sql文件）
mysql -u root -p crm_wechat < sql/init.sql
```

### 4. 配置修改

修改 `crm-wechat-remote/src/main/resources/bootstrap.properties`:

```properties
# 数据库配置
spring.datasource.url=**********************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password

# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=your_redis_password

# ZooKeeper配置
dubbo.registry.address=zookeeper://localhost:2181
```

### 5. 构建项目

```bash
# 全量构建
mvn clean package -DskipTests

# 或者快速编译
mvn clean compile -DskipTests
```

### 6. 启动应用

```bash
# 使用启动脚本（推荐）
./start.sh

# 或者直接运行JAR包
java -jar crm-wechat-remote/target/crm-wechat-remote-*.jar

# 开发模式启动
mvn spring-boot:run -pl crm-wechat-remote
```

### 7. 验证服务

```bash
# 健康检查
curl http://localhost:8080/actuator/health

# 查看应用信息
curl http://localhost:8080/actuator/info
```

## 模块结构

```
crm-wechat/
├── crm-wechat-client/           # Dubbo接口定义、请求/响应对象
│   ├── src/main/java/com/howbuy/crm/wechat/client/
│   │   ├── facade/             # Dubbo接口定义
│   │   ├── domain/request/     # 请求参数对象
│   │   ├── domain/response/    # 响应参数对象
│   │   ├── enums/              # 枚举类
│   │   └── base/               # 基础类
│   └── pom.xml
├── crm-wechat-dao/            # 数据访问层
│   ├── src/main/java/com/howbuy/crm/wechat/dao/
│   │   ├── mapper/             # MyBatis Mapper接口
│   │   └── po/                 # 数据库实体对象
│   └── pom.xml
├── crm-wechat-service/        # 业务逻辑实现层
│   ├── src/main/java/com/howbuy/crm/wechat/service/
│   │   ├── facade/            # Dubbo接口实现
│   │   ├── service/           # 业务服务层
│   │   ├── controller/        # HTTP接口层
│   │   ├── repository/        # 数据访问层实现
│   │   ├── business/          # 公共业务逻辑
│   │   ├── job/               # 定时任务
│   │   └── outerservice/      # 外部接口调用
│   └── pom.xml
├── crm-wechat-remote/         # 启动模块和配置
│   ├── src/main/java/com/howbuy/crm/wechat/remote/
│   │   └── CrmWechatApplication.java  # 启动类
│   ├── src/main/resources/    # 配置文件
│   └── pom.xml
├── openspec/                  # 规格说明文档
├── sql/                       # 数据库脚本
├── docs/                      # 项目文档
├── pom.xml                    # 父POM文件
└── README.md                  # 项目说明文档
```

## 核心功能

### 多租户支持

- 支持多个企业微信账号统一管理
- 基于CompanyNoEnum枚举的企业配置管理
- 支持内建应用和第三方应用配置

### 微信API集成

- **部门管理**: 同步企业部门结构
- **员工管理**: 企业员工信息同步和管理
- **客户管理**: 外部联系人管理和关系绑定
- **群聊管理**: 微信群创建、成员管理、消息推送
- **素材管理**: 图片、视频、文件素材上传和管理
- **消息推送**: 模板消息、客服消息推送
- **JSSDK支持**: 微信JSSDK配置和票据管理

### 业务功能

- **客户关系管理**: 微信用户与系统客户绑定
- **消息中心**: 统一消息发送和接收管理
- **回调处理**: 微信事件回调处理和消息路由
- **定时任务**: 数据同步和消息处理任务

## 部署指南

### 开发环境部署

```bash
# 1. 构建项目
mvn clean package -DskipTests

# 2. 启动应用
./start.sh

# 3. 查看日志
tail -f logs/cmsstdout.log
```

### 生产环境部署

```bash
# 1. 构建生产包
mvn clean package -Pprod -DskipTests

# 2. 复制到部署目录
cp crm-wechat-remote/target/crm-wechat-remote-*.jar /opt/app/

# 3. 启动服务
cd /opt/app
java -Xms2g -Xmx4g -jar crm-wechat-remote-*.jar

# 4. 配置开机自启
systemctl enable crm-wechat
systemctl start crm-wechat
```

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY crm-wechat-remote/target/crm-wechat-remote-*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```bash
# 构建镜像
docker build -t crm-wechat:latest .

# 运行容器
docker run -d -p 8080:8080 --name crm-wechat crm-wechat:latest
```

## 配置说明

### 重要配置文件

- `bootstrap.properties`: 启动配置
- `dubbo.xml`: Dubbo服务配置
- `log4j2-*.xml`: 日志配置
- `spring/application-*.xml`: Spring配置

### 环境配置

支持多环境配置：dev、test、prod

### 微信配置

需要在数据库中配置企业微信相关参数：

- 企业ID (corpid)
- 应用Secret
- 回调URL和Token
- API调用凭证缓存

## 监控和运维

### 健康检查

- 应用健康状态: `/actuator/health`
- 应用信息: `/actuator/info`
- Dubbo服务监控: `/dubbo-admin`
- 数据库连接池监控: Druid监控页面

### 日志管理

```bash
# 查看应用日志
tail -f logs/cmsstdout.log

# 查看错误日志
grep ERROR logs/cmsstdout.log

# 查看微信相关日志
grep "wechat" logs/cmsstdout.log
```

### 常用运维命令

```bash
# 重启应用
./start.sh restart

# 停止应用
./start.sh stop

# 调试模式启动
./start.sh debug

# JMX监控启动
./start.sh jmx
```

## 开发规范

### 代码规范

- 使用 `@Setter/@Getter`注解（不使用 `@Data`）
- 禁止使用 `BeanUtils.copyProperties`
- 字符串判空使用 `StringUtils.isEmpty`
- 统一异常处理和错误码

### 事务管理

- Repository层: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
- 数据修改: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
- 外部接口调用: 统一在outerservice包中

### 接口规范

- Dubbo接口继承 `BaseFacade<Request, Response>`
- 请求类继承 `BaseRequest`
- 统一使用 `Response<T>`包装响应结果

## 性能优化

### 缓存策略

- AccessToken和JsapiTicket缓存（2小时有效期）
- 用户信息和部门信息多级缓存
- Redis集群部署

### 数据库优化

- 合理设计索引
- 批量操作优化
- 连接池参数调优
- 读写分离

### 异步处理

- 消息发送异步化
- 文件上传异步处理
- 数据同步异步批处理

## 故障排查

### 常见问题

1. **启动失败**: 检查端口占用和依赖服务状态
2. **数据库连接失败**: 检查数据库配置和网络连通性
3. **Redis连接失败**: 检查Redis服务状态和配置
4. **微信API调用失败**: 检查AccessToken和权限配置

### 调试模式

```bash
# 远程调试启动
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar crm-wechat-remote-*.jar
```

## 更新日志

### v2.0.4.2 (2025-11-04)

- 优化客户标签同步功能
- 修复群聊管理相关问题
- 提升系统性能和稳定性

### v2.0.4.1

- 新增多租户支持
- 优化消息推送机制
- 增强安全特性

## 贡献指南

1. Fork项目到个人仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交代码: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request
# test
